# Pokemon Versus Workflow Testing Guide

## Pre-Testing Checklist

### 1. API Credentials Setup
Ensure all required API keys are configured:
- [ ] Google Sheets OAuth2 credentials
- [ ] OpenRouter API key for GPT models
- [ ] PIAPI authentication for image generation
- [ ] Creatomate API key and template ID

### 2. Google Sheets Preparation
Create a test sheet with these columns:
- [ ] Main Pokemon (e.g., "Charizard")
- [ ] Pokemon Type (e.g., "Legendary", "Dragon", "Starter")
- [ ] Status (set to "To Do" for testing)
- [ ] Final Video (leave empty)
- [ ] Image columns 1.1 through 8.3 (leave empty)

### 3. Test Data Setup
Add these test rows to your Google Sheet:

| Main Pokemon | Pokemon Type | Status |
|--------------|--------------|--------|
| Pikachu      | Legendary    | To Do  |
| Charizard    | Dragon       | To Do  |
| Mewtwo       | Starter      | To Do  |

## Testing Phases

### Phase 1: Individual Node Testing

#### Test 1: Get Main Pokemon Node
1. Run only the "Get Main Pokemon" node
2. **Expected Output**: Should return the first row with Status = "To Do"
3. **Verify**: Main Pokemon and Pokemon Type fields are populated

#### Test 2: Pokemon Battle Creator Node
1. Run "Get Main Pokemon" → "Pokemon Battle Creator"
2. **Expected Output**: 8 different Pokemon opponents
3. **Verify**: 
   - All Pokemon names are valid
   - No duplicates in the list
   - Opponents match the requested type category

#### Test 3: Pokemon Image Prompt Generator
1. Run the image prompt generation chain
2. **Expected Output**: Two distinct prompts for main Pokemon and opponent
3. **Verify**:
   - Prompts mention official Pokemon art style
   - Include specific Pokemon names
   - Describe battle-ready poses and energy effects

### Phase 2: Image Generation Testing

#### Test 4: Pokemon Close-Up Generation
1. Run the full close-up generation workflow
2. **Expected Output**: High-quality Pokemon artwork URLs
3. **Verify**:
   - Images load correctly
   - Pokemon are recognizable and accurate
   - Art style matches Pokemon aesthetic
   - Battle poses are dynamic

#### Test 5: Battle Scene Generation
1. Test the Pokemon battle winner prompt generation
2. **Expected Output**: Dynamic battle scene images
3. **Verify**:
   - Both Pokemon are visible in the scene
   - Battle effects and energy are present
   - Winner/loser dynamic is clear
   - Background matches Pokemon battle environment

### Phase 3: End-to-End Testing

#### Test 6: Complete Workflow
1. Set one row to Status = "To Do"
2. Run the entire workflow
3. **Expected Results**:
   - Status changes to "Created"
   - All image columns (1.1-8.3) populated with URLs
   - Final Video column contains Creatomate video URL
   - Video plays correctly with all 8 battle scenes

#### Test 7: Multiple Pokemon Testing
1. Set multiple rows to "To Do"
2. Run workflow
3. **Verify**: Each Pokemon gets unique opponents and battles

## Validation Checklist

### Pokemon Accuracy
- [ ] All generated Pokemon names are real Pokemon
- [ ] Type matchups make sense (Fire vs Water, etc.)
- [ ] Legendary Pokemon are appropriately powerful
- [ ] Starter Pokemon have balanced matchups

### Image Quality
- [ ] Pokemon are visually accurate to official designs
- [ ] Colors match official Pokemon color schemes
- [ ] Battle poses are dynamic and engaging
- [ ] Energy effects match Pokemon types (fire, water, electric, etc.)

### Video Output
- [ ] All 8 scenes are included in final video
- [ ] Transitions between scenes are smooth
- [ ] Audio/music plays correctly
- [ ] Video duration is appropriate (40 seconds total)

### Google Sheets Integration
- [ ] Data reads correctly from sheets
- [ ] Image URLs are written back to correct columns
- [ ] Status updates properly
- [ ] No data corruption or overwrites

## Troubleshooting Common Issues

### Issue 1: No Pokemon Found
**Symptoms**: Empty opponent list or "No Pokemon" errors
**Solutions**:
- Check Pokemon Type spelling in Google Sheets
- Verify AI model is responding correctly
- Ensure OpenRouter API key is valid

### Issue 2: Image Generation Fails
**Symptoms**: Empty image URLs or error messages
**Solutions**:
- Check PIAPI API limits and credits
- Verify image prompts are not too long
- Test with simpler Pokemon names first

### Issue 3: Video Creation Fails
**Symptoms**: No final video URL generated
**Solutions**:
- Verify Creatomate template ID is correct
- Check that all image URLs are accessible
- Ensure Creatomate API key has proper permissions

### Issue 4: Google Sheets Errors
**Symptoms**: Permission errors or data not updating
**Solutions**:
- Refresh OAuth2 credentials
- Check sheet sharing permissions
- Verify column names match exactly

## Performance Optimization

### Recommended Settings
- **Wait times**: 90 seconds between image generation calls
- **Batch size**: Process 1 Pokemon at a time initially
- **Error handling**: Enable retry on API failures
- **Logging**: Turn on debug mode for troubleshooting

### Scaling Considerations
- Monitor API rate limits (especially PIAPI)
- Consider adding random delays between calls
- Implement queue system for multiple Pokemon processing
- Set up monitoring for failed workflows

## Success Metrics

A successful test should achieve:
- [ ] 100% Pokemon name accuracy
- [ ] 95%+ image generation success rate
- [ ] 90%+ video creation success rate
- [ ] All Google Sheets data properly updated
- [ ] No workflow errors or timeouts

## Next Steps After Testing

1. **Production Deployment**: Move to production Google Sheets
2. **Monitoring Setup**: Implement error tracking and alerts
3. **Content Review**: Establish process for reviewing generated content
4. **Scaling**: Gradually increase Pokemon processing volume
5. **Optimization**: Fine-tune prompts based on output quality

## Support Resources

- n8n workflow execution logs
- PIAPI generation history
- Creatomate render status
- Google Sheets API quotas
- OpenRouter usage statistics
