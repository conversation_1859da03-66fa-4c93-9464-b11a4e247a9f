{"pokemon_versus_sheets_structure": {"columns": {"A": {"name": "Main Pokemon", "description": "The primary Pokemon for the versus scenario", "example_values": ["<PERSON><PERSON><PERSON>", "Mewtwo", "<PERSON><PERSON><PERSON>", "Ray<PERSON><PERSON>"]}, "B": {"name": "Pokemon Type", "description": "The type category for opponent selection", "example_values": ["Legendary", "Starter", "Electric", "Dragon"]}, "C": {"name": "Status", "description": "Current processing status", "example_values": ["To Do", "Processing", "Created", "Published"]}, "D": {"name": "Final Video", "description": "URL to the completed video", "example_values": ["https://creatomate.com/video/xyz"]}, "E": {"name": "1.1", "description": "Battle Scene 1 - <PERSON> close-up image", "example_values": ["https://piapi.ai/image/pokemon1_closeup"]}, "F": {"name": "1.2", "description": "Battle Scene 1 - Opponent <PERSON><PERSON><PERSON> close-up image", "example_values": ["https://piapi.ai/image/opponent1_closeup"]}, "G": {"name": "1.3", "description": "Battle Scene 1 - Battle aftermath/winner image", "example_values": ["https://piapi.ai/image/battle1_winner"]}, "H": {"name": "2.1", "description": "Battle Scene 2 - <PERSON> close-up image", "example_values": ["https://piapi.ai/image/pokemon2_closeup"]}, "I": {"name": "2.2", "description": "Battle Scene 2 - Opponent <PERSON><PERSON><PERSON> close-up image", "example_values": ["https://piapi.ai/image/opponent2_closeup"]}, "J": {"name": "2.3", "description": "Battle Scene 2 - Battle aftermath/winner image", "example_values": ["https://piapi.ai/image/battle2_winner"]}}, "sample_data": [{"Main Pokemon": "<PERSON><PERSON><PERSON>", "Pokemon Type": "Legendary", "Status": "To Do", "Final Video": "", "1.1": "", "1.2": "", "1.3": "", "2.1": "", "2.2": "", "2.3": "", "3.1": "", "3.2": "", "3.3": "", "4.1": "", "4.2": "", "4.3": "", "5.1": "", "5.2": "", "5.3": "", "6.1": "", "6.2": "", "6.3": "", "7.1": "", "7.2": "", "7.3": "", "8.1": "", "8.2": "", "8.3": ""}, {"Main Pokemon": "<PERSON><PERSON><PERSON>", "Pokemon Type": "Dragon", "Status": "To Do", "Final Video": "", "1.1": "", "1.2": "", "1.3": "", "2.1": "", "2.2": "", "2.3": "", "3.1": "", "3.2": "", "3.3": "", "4.1": "", "4.2": "", "4.3": "", "5.1": "", "5.2": "", "5.3": "", "6.1": "", "6.2": "", "6.3": "", "7.1": "", "7.2": "", "7.3": "", "8.1": "", "8.2": "", "8.3": ""}, {"Main Pokemon": "Mewtwo", "Pokemon Type": "Starter", "Status": "To Do", "Final Video": "", "1.1": "", "1.2": "", "1.3": "", "2.1": "", "2.2": "", "2.3": "", "3.1": "", "3.2": "", "3.3": "", "4.1": "", "4.2": "", "4.3": "", "5.1": "", "5.2": "", "5.3": "", "6.1": "", "6.2": "", "6.3": "", "7.1": "", "7.2": "", "7.3": "", "8.1": "", "8.2": "", "8.3": ""}], "workflow_integration": {"trigger_column": "Status", "trigger_value": "To Do", "update_columns": ["Status", "Final Video", "1.1", "1.2", "1.3", "2.1", "2.2", "2.3", "3.1", "3.2", "3.3", "4.1", "4.2", "4.3", "5.1", "5.2", "5.3", "6.1", "6.2", "6.3", "7.1", "7.2", "7.3", "8.1", "8.2", "8.3"]}}}