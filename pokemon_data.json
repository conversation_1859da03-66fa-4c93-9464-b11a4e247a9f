{"pokemon_database": {"legendary_pokemon": [{"name": "Mewtwo", "type": ["Psychic"], "generation": 1, "max_level_stats": {"hp": 416, "attack": 350, "defense": 306, "sp_attack": 447, "sp_defense": 306, "speed": 394}, "signature_moves": ["Psystrike", "Psychic", "Shadow Ball"], "image_prompt_keywords": "purple and white psychic cat-like legendary <PERSON><PERSON><PERSON> with intense glowing eyes"}, {"name": "<PERSON><PERSON>", "type": ["Normal"], "generation": 4, "max_level_stats": {"hp": 444, "attack": 372, "defense": 372, "sp_attack": 372, "sp_defense": 372, "speed": 372}, "signature_moves": ["Judgment", "Hyper Beam", "Extreme Speed"], "image_prompt_keywords": "white and gold divine creator <PERSON><PERSON><PERSON> with ring-like body and glowing wheel"}, {"name": "Ray<PERSON><PERSON>", "type": ["Dragon", "Flying"], "generation": 3, "max_level_stats": {"hp": 432, "attack": 438, "defense": 306, "sp_attack": 438, "sp_defense": 306, "speed": 317}, "signature_moves": ["Dragon Ascent", "Outrage", "Air Slash"], "image_prompt_keywords": "massive green serpentine dragon <PERSON><PERSON><PERSON> with yellow markings flying through the sky"}], "starter_pokemon": [{"name": "<PERSON><PERSON><PERSON>", "type": ["Fire", "Flying"], "generation": 1, "max_level_stats": {"hp": 360, "attack": 293, "defense": 280, "sp_attack": 317, "sp_defense": 295, "speed": 328}, "signature_moves": ["Flamethrower", "Dragon Pulse", "Air Slash"], "image_prompt_keywords": "orange dragon-like Pokemon with large wings and flame on tail breathing fire"}, {"name": "Blastoise", "type": ["Water"], "generation": 1, "max_level_stats": {"hp": 362, "attack": 291, "defense": 328, "sp_attack": 295, "sp_defense": 317, "speed": 280}, "signature_moves": ["Hydro Pump", "Ice Beam", "Earthquake"], "image_prompt_keywords": "large blue turtle Po<PERSON><PERSON> with water cannons on shell and powerful stance"}, {"name": "<PERSON><PERSON><PERSON>", "type": ["Grass", "Poison"], "generation": 1, "max_level_stats": {"hp": 364, "attack": 291, "defense": 291, "sp_attack": 328, "sp_defense": 328, "speed": 284}, "signature_moves": ["Solar Beam", "Sludge Bomb", "Earthquake"], "image_prompt_keywords": "large blue-green quadruped Po<PERSON>mon with flower on back and vine whips"}], "pseudo_legendary": [{"name": "Dragonite", "type": ["Dragon", "Flying"], "generation": 1, "max_level_stats": {"hp": 386, "attack": 403, "defense": 317, "sp_attack": 328, "sp_defense": 328, "speed": 284}, "signature_moves": ["Dragon Rush", "Hurricane", "Extreme Speed"], "image_prompt_keywords": "orange friendly dragon <PERSON><PERSON><PERSON> with small wings and kind eyes but powerful build"}, {"name": "Garchomp", "type": ["Dragon", "Ground"], "generation": 4, "max_level_stats": {"hp": 440, "attack": 394, "defense": 317, "sp_attack": 284, "sp_defense": 295, "speed": 334}, "signature_moves": ["Dragon Rush", "Earthquake", "<PERSON>"], "image_prompt_keywords": "blue and red shark-like dragon <PERSON><PERSON><PERSON> with scythe-like arms and fierce expression"}], "popular_pokemon": [{"name": "<PERSON><PERSON><PERSON>", "type": ["Electric"], "generation": 1, "max_level_stats": {"hp": 274, "attack": 229, "defense": 196, "sp_attack": 218, "sp_defense": 218, "speed": 306}, "signature_moves": ["Thunderbolt", "Quick Attack", "Iron Tail"], "image_prompt_keywords": "small yellow electric mouse <PERSON><PERSON><PERSON> with red cheeks and lightning bolt tail"}, {"name": "<PERSON><PERSON>", "type": ["Fighting", "Steel"], "generation": 4, "max_level_stats": {"hp": 344, "attack": 350, "defense": 262, "sp_attack": 383, "sp_defense": 262, "speed": 306}, "signature_moves": ["Aura Sphere", "Close Combat", "<PERSON>"], "image_prompt_keywords": "blue and black jackal-like <PERSON><PERSON><PERSON> with aura sensing abilities and martial arts stance"}]}, "type_effectiveness": {"Fire": {"super_effective": ["Grass", "Ice", "Bug", "Steel"], "not_very_effective": ["Fire", "Water", "Rock", "Dragon"], "no_effect": []}, "Water": {"super_effective": ["Fire", "Ground", "Rock"], "not_very_effective": ["Water", "Grass", "Dragon"], "no_effect": []}, "Grass": {"super_effective": ["Water", "Ground", "Rock"], "not_very_effective": ["Fire", "Grass", "Poison", "Flying", "Bug", "Dragon", "Steel"], "no_effect": []}, "Electric": {"super_effective": ["Water", "Flying"], "not_very_effective": ["Electric", "Grass", "Dragon"], "no_effect": ["Ground"]}, "Psychic": {"super_effective": ["Fighting", "Poison"], "not_very_effective": ["Psychic", "Steel"], "no_effect": ["Dark"]}, "Dragon": {"super_effective": ["Dragon"], "not_very_effective": ["Steel"], "no_effect": ["Fairy"]}}}