# Pokemon Versus n8n Workflow

This project contains an n8n workflow for automatically generating Pokemon versus battle videos using AI image generation and Google Sheets integration.

## Overview

The workflow creates engaging Pokemon battle videos by:
1. Reading Pokemon data from Google Sheets
2. Generating opponent Pokemon based on type categories
3. Creating AI-generated Pokemon artwork for battles
4. Producing battle scene videos using Creatomate
5. Updating Google Sheets with generated content

## Files Structure

- `AI_Versus.json` - Main n8n workflow file (modified for Pokemon)
- `pokemon_data.json` - Pokemon database with stats and information
- `pokemon_sheets_template.json` - Google Sheets structure template
- `Creatomate Template.txt` - Video template configuration
- `README.md` - This documentation file

## Setup Instructions

### 1. Google Sheets Setup

Create a Google Sheet with the following columns:
- **Main Pokemon**: The primary Pokemon for battles (e.g., "Charizard")
- **Pokemon Type**: Category for opponent selection (e.g., "Legendary", "Dragon")
- **Status**: Processing status ("To Do", "Processing", "Created")
- **Final Video**: URL to completed video
- **1.1 - 8.3**: Image URLs for 8 battle scenes (3 images each)

### 2. Required API Keys

You'll need the following API credentials:
- **Google Sheets API**: For reading/writing Pokemon data
- **OpenRouter API**: For AI text generation (GPT models)
- **PIAPI**: For Pokemon artwork generation
- **Creatomate API**: For video creation

### 3. n8n Workflow Import

1. Import `AI_Versus.json` into your n8n instance
2. Configure the following nodes with your credentials:
   - Google Sheets nodes: Add your Google Sheets OAuth2 credentials
   - OpenRouter nodes: Add your OpenRouter API key
   - PIAPI nodes: Add your PIAPI authentication
   - Creatomate node: Add your Creatomate API key

### 4. Workflow Configuration

Update the following parameters in the workflow:

#### Google Sheets Node
- Document ID: Your Google Sheets document ID
- Sheet name: Your worksheet name

#### Creatomate Node
- Template ID: Your Creatomate template ID
- API Key: Your Creatomate bearer token

#### PIAPI Nodes
- Model: "Qubico/flux1-dev" (or preferred model)
- Image dimensions: 1024x1024 for close-ups, 540x960 for scenes

## How It Works

### 1. Data Input
The workflow reads from Google Sheets to get:
- Main Pokemon name
- Pokemon type category for opponent selection
- Current processing status

### 2. Opponent Generation
Using AI (GPT-4), the workflow generates 8 different opponent Pokemon based on:
- Main Pokemon characteristics
- Type category specified
- Battle viability and diversity

### 3. Image Generation
For each battle scenario, the workflow creates:
- **Close-up images**: Main Pokemon and opponent in battle-ready poses
- **Battle scenes**: Dynamic artwork showing the climactic battle moment

### 4. Video Creation
Creatomate combines all images into a dynamic video with:
- 8 battle scenes (5 seconds each)
- Background music
- Text overlays and transitions

### 5. Data Update
The workflow updates Google Sheets with:
- Generated image URLs
- Final video URL
- Updated status

## Pokemon Data Structure

The `pokemon_data.json` file contains:
- **Legendary Pokemon**: Mewtwo, Arceus, Rayquaza, etc.
- **Starter Pokemon**: Charizard, Blastoise, Venusaur, etc.
- **Pseudo-Legendary**: Dragonite, Garchomp, etc.
- **Popular Pokemon**: Pikachu, Lucario, etc.
- **Type Effectiveness**: Battle advantage calculations

## Customization

### Adding New Pokemon
Edit `pokemon_data.json` to add new Pokemon with:
```json
{
  "name": "Pokemon Name",
  "type": ["Type1", "Type2"],
  "generation": 1,
  "max_level_stats": { ... },
  "signature_moves": ["Move1", "Move2"],
  "image_prompt_keywords": "description for AI generation"
}
```

### Modifying Battle Logic
Update the AI prompts in the workflow nodes to change:
- Opponent selection criteria
- Image generation style
- Battle outcome determination

### Video Template Changes
Modify the Creatomate template to adjust:
- Video duration and timing
- Visual effects and transitions
- Text overlays and styling

## Troubleshooting

### Common Issues
1. **API Rate Limits**: Add wait nodes between API calls
2. **Image Generation Failures**: Check PIAPI model availability
3. **Google Sheets Errors**: Verify OAuth2 permissions
4. **Video Creation Issues**: Confirm Creatomate template ID

### Debug Tips
- Enable debug mode in n8n workflow
- Check individual node outputs
- Verify API response formats
- Test with single Pokemon first

## Example Usage

1. Add a row to Google Sheets:
   - Main Pokemon: "Pikachu"
   - Pokemon Type: "Legendary"
   - Status: "To Do"

2. The workflow will:
   - Generate 8 legendary opponents for Pikachu
   - Create battle artwork for each matchup
   - Produce a complete versus video
   - Update the sheet with results

## Support

For issues or questions:
1. Check n8n workflow execution logs
2. Verify all API credentials are valid
3. Ensure Google Sheets permissions are correct
4. Test individual workflow nodes

## License

This project is for educational and personal use. Pokemon is a trademark of Nintendo/Game Freak/Creatures Inc.
